<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="EcommerceMAUI.Views.ShippingAddressView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:converters="clr-namespace:EcommerceMAUI.Converters"
    x:Name="AddressPage"
    Title="Address">
    <ContentPage.Resources>
        <ResourceDictionary>
            <converters:InverseBooleanConverter x:Key="InverseBoolConverter" />
        </ResourceDictionary>
    </ContentPage.Resources>
    <Grid Margin="16">
        <CollectionView IsVisible="{Binding IsLoaded}" ItemsSource="{Binding Addressess}">
            <CollectionView.ItemsLayout>
                <GridItemsLayout Orientation="Vertical" VerticalItemSpacing="32" />
            </CollectionView.ItemsLayout>
            <CollectionView.ItemTemplate>
                <DataTemplate>
                    <StackLayout Spacing="10">
                        <Label
                            FontAttributes="Bold"
                            FontSize="18"
                            Text="{Binding AddressType}"
                            TextColor="Black" />
                        <Grid ColumnDefinitions="*,18,24">
                            <Label
                                Grid.Column="0"
                                FontSize="14"
                                HorizontalOptions="Start"
                                Text="{Binding FullAddress}"
                                TextColor="Black" />
                            <Label
                                Grid.Column="2"
                                FontFamily="MaterialIcon"
                                FontSize="24"
                                HorizontalOptions="Center"
                                IsVisible="{Binding IsSelected}"
                                Text="&#xf133;"
                                TextColor="{DynamicResource Primary}"
                                VerticalOptions="Center" />
                        </Grid>
                        <StackLayout.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={x:Reference AddressPage}, Path=BindingContext.SelectAddressCommand}" CommandParameter="{Binding .}" />
                        </StackLayout.GestureRecognizers>
                    </StackLayout>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>
        <ActivityIndicator
            HeightRequest="45"
            HorizontalOptions="Center"
            IsRunning="{Binding IsLoaded, Converter={StaticResource InverseBoolConverter}}"
            VerticalOptions="Center"
            WidthRequest="45"
            Color="{StaticResource Primary}" />
    </Grid>
</ContentPage>