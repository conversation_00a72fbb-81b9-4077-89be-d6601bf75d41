<?xml version="1.0" encoding="UTF-8" ?>
<Shell
    x:Class="DefaultTemplateWithContent.AppShell"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:sf="clr-namespace:Syncfusion.Maui.Toolkit.SegmentedControl;assembly=Syncfusion.Maui.Toolkit"
    xmlns:pages="clr-namespace:DefaultTemplateWithContent.Pages"
    Shell.FlyoutBehavior="Flyout"
    Title="DefaultTemplateWithContent">

    <ShellContent
        Title="Dashboard"
        Icon="{StaticResource IconDashboard}"
        ContentTemplate="{DataTemplate pages:MainPage}"
        Route="main" />

    <ShellContent
        Title="Projects"
        Icon="{StaticResource IconProjects}"
        ContentTemplate="{DataTemplate pages:ProjectListPage}"
        Route="projects" />

    <ShellContent
        Title="Manage Meta"
        Icon="{StaticResource IconMeta}"
        ContentTemplate="{DataTemplate pages:ManageMetaPage}"
        Route="manage" />

    <Shell.FlyoutFooter>
        <Grid Padding="15">
            <sf:SfSegmentedControl x:Name="ThemeSegmentedControl" 
                VerticalOptions="Center" HorizontalOptions="Center" SelectionChanged="SfSegmentedControl_SelectionChanged"
                SegmentWidth="40" SegmentHeight="40">
                <sf:SfSegmentedControl.ItemsSource>
                    <x:Array Type="{x:Type sf:SfSegmentItem}">
                        <sf:SfSegmentItem ImageSource="{StaticResource IconLight}"/>
                        <sf:SfSegmentItem ImageSource="{StaticResource IconDark}"/>
                    </x:Array>
                </sf:SfSegmentedControl.ItemsSource>
            </sf:SfSegmentedControl>
        </Grid>
    </Shell.FlyoutFooter>

</Shell>
