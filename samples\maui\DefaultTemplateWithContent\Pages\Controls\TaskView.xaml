<?xml version="1.0" encoding="utf-8" ?>
<Border
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:effectsView="clr-namespace:Syncfusion.Maui.Toolkit.EffectsView;assembly=Syncfusion.Maui.Toolkit"
    xmlns:pageModels="clr-namespace:DefaultTemplateWithContent.PageModels"
    xmlns:models="clr-namespace:DefaultTemplateWithContent.Models"
    xmlns:shimmer="clr-namespace:Syncfusion.Maui.Toolkit.Shimmer;assembly=Syncfusion.Maui.Toolkit"
    x:Class="DefaultTemplateWithContent.Pages.Controls.TaskView"
    StrokeShape="RoundRectangle 20"
    Background="{AppThemeBinding Light={StaticResource LightSecondaryBackground}, Dark={StaticResource DarkSecondaryBackground}}"
    x:DataType="models:ProjectTask">
    
    <effectsView:SfEffectsView
        TouchDownEffects="Highlight"
        HighlightBackground="{AppThemeBinding Light={StaticResource DarkOnLightBackground}, Dark={StaticResource LightOnDarkBackground}}">
        <shimmer:SfShimmer
            BackgroundColor="Transparent"
            VerticalOptions="FillAndExpand"               
            IsActive="{Binding IsBusy, Source={RelativeSource AncestorType={x:Type pageModels:IProjectTaskPageModel}}, x:DataType=pageModels:IProjectTaskPageModel}">
            <shimmer:SfShimmer.CustomView>
                <Grid 
                    ColumnDefinitions="Auto,*"
                    Padding="{OnIdiom 15, Desktop=20}">
                    <BoxView 
                        WidthRequest="24"
                        HeightRequest="24"
                        Margin="12, 0"
                        Style="{StaticResource ShimmerCustomViewStyle}"/>
                    <BoxView 
                        Grid.Column="1"
                        HeightRequest="24"
                        Margin="12, 0"
                        Style="{StaticResource ShimmerCustomViewStyle}"/>
                </Grid>
            </shimmer:SfShimmer.CustomView>
            <shimmer:SfShimmer.Content>
                <Grid ColumnDefinitions="Auto,*" ColumnSpacing="15" Padding="{OnIdiom 15, Desktop=20}">
                    <Grid.GestureRecognizers>
                        <TapGestureRecognizer 
                            Command="{Binding NavigateToTaskCommand, Source={RelativeSource AncestorType={x:Type pageModels:IProjectTaskPageModel}}, x:DataType=pageModels:IProjectTaskPageModel}" 
                            CommandParameter="{Binding .}"/>
                    </Grid.GestureRecognizers>
                    <CheckBox Grid.Column="0" IsChecked="{Binding IsCompleted, Mode=OneTime}" VerticalOptions="Center" CheckedChanged="CheckBox_CheckedChanged"/>
                    <Label Grid.Column="1" Text="{Binding Title}" VerticalOptions="Center" LineBreakMode="TailTruncation"/>
                </Grid>
            </shimmer:SfShimmer.Content>
        </shimmer:SfShimmer>
    </effectsView:SfEffectsView>
</Border>