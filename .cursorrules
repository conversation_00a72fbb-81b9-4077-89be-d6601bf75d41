# Commit Message Format
When generating commit messages, follow these rules:
- Start with a single short summary line (50 characters or less)
- Use imperative mood in the subject line (e.g., "Add feature" not "Added feature")
- Capitalize the first letter of the subject line
- Do not end the subject line with a period
- Separate the subject from the body with a blank line
- If a body is needed, wrap it at 72 characters
