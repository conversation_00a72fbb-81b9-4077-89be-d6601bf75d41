trigger:
  batch: true
  branches:
    include:
    - main
    - 'v*.*'
    - 'validate/*'
  paths:
    exclude:
    - doc/
    - '*.md'
    - .vscode/
    - .github/
    - azure-pipelines/release.yml

parameters:
- name: RunTests
  displayName: Run tests
  type: boolean
  default: false

variables:
  DOTNET_SKIP_FIRST_TIME_EXPERIENCE: true
  BuildConfiguration: Release
  codecov_token: 4dc9e7e2-6b01-4932-a180-847b52b43d35 # Get a new one from https://codecov.io/
  ci_feed: https://pkgs.dev.azure.com/bretjohn-public/_packaging/bretjohn-public/nuget/v3/index.json # Azure Artifacts feed URL
  NUGET_PACKAGES: $(Agent.TempDirectory)/.nuget/packages/

jobs:
- template: azure-pipelines/build.yml
  parameters:
    RunTests: ${{ parameters.RunTests }}
