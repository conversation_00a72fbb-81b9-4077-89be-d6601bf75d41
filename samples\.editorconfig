# EditorConfig is awesome:http://EditorConfig.org

# Use a separate .editorconfig for samples, with fewer settings to keep original conventions from samples intact.
root = true

# Don't use tabs for indentation.
[*]
indent_style = space

# Code files
[*.{cs,csx,vb,vbx,h,cpp,idl}]
indent_size = 4
insert_final_newline = true
trim_trailing_whitespace = true

# MSBuild project files
[*.{csproj,vbproj,vcxproj,vcxproj.filters,proj,projitems,shproj,msbuildproj,props,targets}]
indent_size = 2

# Xml config files
[*.{ruleset,config,nuspec,resx,vsixmanifest,vsct,runsettings}]
indent_size = 2
indent_style = space

# JSON files
[*.json]
indent_size = 2
indent_style = space

# XAML files
[*.xaml]
indent_size = 2
indent_style = space

[*.ps1]
indent_style = space
indent_size = 4
