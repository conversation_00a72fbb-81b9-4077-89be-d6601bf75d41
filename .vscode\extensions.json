{
  // See https://go.microsoft.com/fwlink/?LinkId=827846 to learn about workspace recommendations.
  // Extension identifier format: ${publisher}.${name}. Example: vscode.csharp
  // List of extensions which should be recommended for users of this workspace.
  "recommendations": [
    "ms-azure-devops.azure-pipelines",
    "ms-dotnettools.csharp",
    "k--kato.docomment",
    "editorconfig.editorconfig",
    "esbenp.prettier-vscode",
    "pflannery.vscode-versionlens",
    "davidanson.vscode-markdownlint",
    "dotjoshjohnson.xml",
    "ms-azuretools.vscode-docker",
    "tintoy.msbuild-project-tools"
  ],
  // List of extensions recommended by VS Code that should not be recommended for users of this workspace.
  "unwantedRecommendations": []
}
