<?xml version="1.0" encoding="utf-8"?>
<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>false</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <ItemGroup>
    <!-- Put repo-specific PackageVersion items in this group. -->
  </ItemGroup>
  <ItemGroup Label="Library.Template">
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.14.0" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="3.1.0" />
    <PackageVersion Include="xunit.v3" Version="2.0.2" />
  </ItemGroup>
  <ItemGroup>
    <!-- Put repo-specific GlobalPackageReference items in this group. -->
  </ItemGroup>
  <ItemGroup Label="Library.Template">
    <PackageReference Include="CSharpIsNullAnalyzer" Version="0.1.593" />
    <PackageReference Include="DotNetAnalyzers.DocumentationAnalyzers" Version="1.0.0-beta.59" />
    <!-- The condition works around https://github.com/dotnet/sdk/issues/44951 -->
    <PackageReference Include="Nerdbank.GitVersioning" Version="3.7.115" Condition="!('$(TF_BUILD)'=='true' and '$(dotnetformat)'=='true')" />
    <PackageReference Include="PolySharp" Version="1.15.0" />
  </ItemGroup>
</Project>
