{"name": "Dev space", "dockerFile": "Dockerfile", "customizations": {"vscode": {"settings": {"terminal.integrated.shell.linux": "/usr/bin/pwsh"}, "extensions": ["ms-azure-devops.azure-pipelines", "ms-dotnettools.csharp", "k--kato.docomment", "editorconfig.editorconfig", "esbenp.prettier-vscode", "pflannery.vscode-versionlens", "davidanson.vscode-markdownlint", "dotjoshjohnson.xml", "ms-vscode-remote.remote-containers", "ms-azuretools.vscode-docker", "tintoy.msbuild-project-tools"]}}, "postCreateCommand": "./init.ps1 -InstallLocality machine"}