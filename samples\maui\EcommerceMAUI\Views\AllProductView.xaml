<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="EcommerceMAUI.Views.AllProductView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:converters="clr-namespace:EcommerceMAUI.Converters"
    xmlns:vm="clr-namespace:EcommerceMAUI.ViewModel"
    x:Name="AllProductPage"
    Title="All Products">
    <ContentPage.Resources>
        <ResourceDictionary>
            <converters:InverseBooleanConverter x:Key="InverseBoolConverter" />
        </ResourceDictionary>
    </ContentPage.Resources>
    <Grid>
        <CollectionView
            Margin="12"
            IsVisible="{Binding IsLoaded}"
            ItemsSource="{Binding Products}">
            <CollectionView.ItemsLayout>
                <GridItemsLayout
                    HorizontalItemSpacing="12"
                    Orientation="Vertical"
                    Span="2"
                    VerticalItemSpacing="12" />
            </CollectionView.ItemsLayout>
            <CollectionView.ItemTemplate>
                <DataTemplate>
                    <StackLayout
                        Margin="0"
                        Background="White"
                        HorizontalOptions="FillAndExpand">
                        <Border
                            Background="transparent"
                            StrokeShape="RoundRectangle 6,0,0,6"
                            StrokeThickness="1">
                            <Image
                                Aspect="AspectFit"
                                HeightRequest="240"
                                Source="{Binding ImageUrl}"
                                WidthRequest="165" />
                        </Border>
                        <StackLayout Margin="0,8,0,0">
                            <Label
                                FontSize="16"
                                HorizontalOptions="Center"
                                Text="{Binding Name}"
                                TextColor="Black"
                                VerticalOptions="Start" />
                            <Label
                                FontSize="12"
                                HorizontalOptions="Center"
                                Text="{Binding BrandName}"
                                TextColor="{StaticResource SecondaryTextColor}"
                                VerticalOptions="Start" />
                            <Label
                                FontSize="16"
                                HorizontalOptions="Center"
                                Text="{Binding Price, StringFormat='${0:F2}'}"
                                TextColor="{StaticResource Primary}"
                                VerticalOptions="Start" />
                        </StackLayout>
                        <StackLayout.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={x:Reference AllProductPage}, Path=BindingContext.SelectProductCommand}" CommandParameter="{Binding .}" />
                        </StackLayout.GestureRecognizers>
                    </StackLayout>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>
        <ActivityIndicator
            HeightRequest="45"
            HorizontalOptions="Center"
            IsRunning="{Binding IsLoaded, Converter={StaticResource InverseBoolConverter}}"
            VerticalOptions="Center"
            WidthRequest="45"
            Color="{StaticResource Primary}" />
    </Grid>
</ContentPage>