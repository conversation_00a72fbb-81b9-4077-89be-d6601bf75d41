{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:best-practices"], "labels": ["dependencies"], "packageRules": [{"matchPackageNames": ["nbgv", "nerdbank.gitversioning"], "groupName": "nbgv and nerdbank.gitversioning updates"}, {"matchPackageNames": ["xunit*"], "groupName": "xunit"}, {"matchDatasources": ["dotnet-version", "docker"], "matchDepNames": ["dotnet-sdk", "mcr.microsoft.com/dotnet/sdk"], "groupName": "Dockerfile and global.json updates"}, {"matchPackageNames": ["*"], "allowedVersions": "!/-g[a-f0-9]+$/"}]}