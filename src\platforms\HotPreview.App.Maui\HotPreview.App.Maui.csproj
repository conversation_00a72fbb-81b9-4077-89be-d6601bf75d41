﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>net8.0-android;net8.0-ios;net8.0-maccatalyst</TargetFrameworks>
    <TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net8.0-windows10.0.19041.0</TargetFrameworks>
    <UseMaui>true</UseMaui>
    <SingleProject>true</SingleProject>
    <Nullable>enable</Nullable>

    <!-- Don't use implicit usings, so this code can also be embedded in the VS MauiTap assembly -->
    <ImplicitUsings>disable</ImplicitUsings>

    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">11.0</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">13.1</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</SupportedOSPlatformVersion>
    <TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</TargetPlatformMinVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'tizen'">6.5</SupportedOSPlatformVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Maui.Controls" Version="$(MauiVersion)" />
    <PackageReference Include="Microsoft.Maui.Controls.Compatibility" Version="$(MauiVersion)" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\HotPreview.SharedModel\HotPreview.SharedModel.csproj" GeneratePathProperty="true" />
  </ItemGroup>

  <ItemGroup>
    <!-- Include the targets files in the NuGet package -->
    <None Include="build\HotPreview.App.Maui.targets" Pack="true" PackagePath="build\HotPreview.App.Maui.targets" />
  </ItemGroup>

  <ItemGroup>
    <!-- Images -->
    <MauiImage Include="Resources\IconsLight\*" />
    <MauiImage Include="Resources\IconsDark\*" TintColor="#66B3FF" />

    <!-- Custom Fonts -->
    <MauiFont Include="Resources\Fonts\*" />
  </ItemGroup>

  <!-- Android -->
  <ItemGroup Condition="$(TargetFramework.StartsWith('net8.0-android')) != true">
    <Compile Remove="**\*.Android.cs" />
    <None Include="**\*.Android.cs" Exclude="$(DefaultItemExcludes);$(DefaultExcludesInProjectFolder)" />
  </ItemGroup>

  <!-- Both iOS and Mac Catalyst -->
  <ItemGroup Condition="$(TargetFramework.StartsWith('net8.0-ios')) != true AND $(TargetFramework.StartsWith('net8.0-maccatalyst')) != true">
    <Compile Remove="**\*.MaciOS.cs" />
    <None Include="**\*.MaciOS.cs" Exclude="$(DefaultItemExcludes);$(DefaultExcludesInProjectFolder)" />
  </ItemGroup>

  <!-- iOS -->
  <ItemGroup Condition="$(TargetFramework.StartsWith('net8.0-ios')) != true">
    <Compile Remove="**\*.iOS.cs" />
    <None Include="**\*.iOS.cs" Exclude="$(DefaultItemExcludes);$(DefaultExcludesInProjectFolder)" />
  </ItemGroup>

  <!-- Mac Catalyst -->
  <ItemGroup Condition="$(TargetFramework.StartsWith('net8.0-maccatalyst')) != true">
    <Compile Remove="**\*.MacCatalyst.cs" />
    <None Include="**\*.MacCatalyst.cs" Exclude="$(DefaultItemExcludes);$(DefaultExcludesInProjectFolder)" />
  </ItemGroup>

  <!-- Windows -->
  <ItemGroup Condition="$(TargetFramework.Contains('-windows')) != true">
    <Compile Remove="**\*.Windows.cs" />
    <None Include="**\*.Windows.cs" Exclude="$(DefaultItemExcludes);$(DefaultExcludesInProjectFolder)" />
  </ItemGroup>

  <ItemGroup>
  <Compile Update="App.xaml.cs">
    <DependentUpon>App.xaml</DependentUpon>
  </Compile>
  <Compile Update="Pages\ExampleItemDataTemplateSelector.cs">
    <DependentUpon>%(Filename)</DependentUpon>
  </Compile>
  <Compile Update="Pages\ExamplesPage.xaml.cs">
    <DependentUpon>ExamplesPage.xaml</DependentUpon>
  </Compile>
</ItemGroup>

<ItemGroup>
  <MauiXaml Update="App.xaml">
    <Generator>MSBuild:Compile</Generator>
  </MauiXaml>
  <MauiXaml Update="Resources\Styles\Colors.xaml">
    <Generator>MSBuild:Compile</Generator>
  </MauiXaml>
  <MauiXaml Update="Resources\Styles\Styles.xaml">
    <Generator>MSBuild:Compile</Generator>
  </MauiXaml>
</ItemGroup>

<ItemGroup>
  <MauiImage Update="Resources\IconsLight\ic_preview_control__light.svg">
    <TintColor>#66B3FF</TintColor>
  </MauiImage>
  <MauiImage Update="Resources\IconsLight\ic_preview_page__light.svg">
    <TintColor>#66B3FF</TintColor>
  </MauiImage>
</ItemGroup>

</Project>
