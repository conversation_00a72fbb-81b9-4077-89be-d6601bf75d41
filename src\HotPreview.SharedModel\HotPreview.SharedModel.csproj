<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
    <ImplicitUsings>disable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.4" />
    <PackageReference Include="StreamJsonRpc" Version="2.22.11" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\HotPreview\HotPreview.csproj" />
  </ItemGroup>

  <ItemGroup>
    <!-- Include the AppBuildTasks assembly and associated targets file in the NuGet package, so the platform NuGets can use the task -->
    <None Include="..\..\bin\HotPreview.AppBuildTasks\$(Configuration)\netstandard2.0\HotPreview.AppBuildTasks.dll" Pack="true" PackagePath="buildTransitive\HotPreview.AppBuildTasks.dll" />
    <None Include="buildTransitive\HotPreview.SharedModel.targets" Pack="true" PackagePath="buildTransitive\HotPreview.SharedModel.targets" />
  </ItemGroup>

</Project>
