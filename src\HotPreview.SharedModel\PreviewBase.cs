namespace HotPreview.SharedModel;

public abstract class PreviewBase(string? displayNameOverride)
{
    private readonly string? _displayNameOverride = displayNameOverride;

    public virtual bool IsAutoGenerated => false;

    /// <summary>
    /// DisplayName is intended to be what's shown in UI to identify the preview. It can contain spaces and
    /// isn't necessarily unique. It defaults to the preview method/class name (with no namespace) but can
    /// be overridden by the developer.
    /// </summary>
    public string DisplayName => _displayNameOverride ?? NameUtilities.GetUnqualifiedName(Name);

    public string? DisplayNameOverride => _displayNameOverride;

    /// <summary>
    /// Name is intended to be used by code to uniquely identify the preview. It's the preview's
    /// full qualified method name.
    /// </summary>
    public abstract string Name { get; }
}
