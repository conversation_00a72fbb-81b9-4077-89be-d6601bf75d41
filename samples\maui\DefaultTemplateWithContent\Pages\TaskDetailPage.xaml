<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:sf="clr-namespace:Syncfusion.Maui.Toolkit.TextInputLayout;assembly=Syncfusion.Maui.Toolkit"
             xmlns:pageModel="clr-namespace:DefaultTemplateWithContent.PageModels"
             xmlns:models="clr-namespace:DefaultTemplateWithContent.Models"
             x:DataType="pageModel:TaskDetailPageModel"
             x:Class="DefaultTemplateWithContent.Pages.TaskDetailPage"
             Title="Task">

    <ContentPage.ToolbarItems>
        <ToolbarItem
            Text="Delete"
            Command="{Binding DeleteCommand}"
            Order="Primary"
            Priority="0"
            IconImageSource="{StaticResource IconDelete}" />
    </ContentPage.ToolbarItems>        

    <Grid>
        <ScrollView>
            <VerticalStackLayout Spacing="{StaticResource LayoutSpacing}" Padding="{StaticResource LayoutPadding}">
                <sf:SfTextInputLayout 
                    Hint="Task">
                    <Entry
                        Text="{Binding Title}" />
                </sf:SfTextInputLayout>

                <sf:SfTextInputLayout 
                    Hint="Completed">
                    <CheckBox
                        HorizontalOptions="End"
                        IsChecked="{Binding IsCompleted}" />
                </sf:SfTextInputLayout>

                <sf:SfTextInputLayout 
                    IsVisible="{Binding IsExistingProject}" 
                    Hint="Project">
                    <Picker 
                        ItemsSource="{Binding Projects}"
                        ItemDisplayBinding="{Binding Name, x:DataType=models:Project}"
                        SelectedItem="{Binding Project}"
                        SelectedIndex="{Binding SelectedProjectIndex}" />
                </sf:SfTextInputLayout>

                <Button 
                    HeightRequest="{OnIdiom 44, Desktop=60}"
                    Text="Save"
                    Command="{Binding SaveCommand}"/>
            </VerticalStackLayout>
        </ScrollView>
    </Grid>
</ContentPage>