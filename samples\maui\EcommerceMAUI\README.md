This code comes from https://github.com/exendahal/ecommerce_maui

# Ecommerce Design

E-commerce design with .NET MAUI (MVVM Pattern)

Learn More: <https://docs.microsoft.com/en-us/dotnet/maui/what-is-maui>

## Features

* ColletionView
* SwipeView
* MVVM
* Font Icon
* Borders
* Color gradient brushes
* Stepper

## Generate C# code from icon font

Link: <https://andreinitescu.github.io/IconFont2Code/>

## Screenshots

<img src="Preview/1.png" width="250" height="520"> &nbsp;&nbsp;&nbsp;
<img src="Preview/2.png" width="250" height="520"> &nbsp;&nbsp;&nbsp;
<img src="Preview/3.png" width="250" height="520"> <br>
<img src="Preview/4.png" width="250" height="520"> &nbsp;&nbsp;&nbsp;
<img src="Preview/5.png" width="250" height="520"> &nbsp;&nbsp;&nbsp;
<img src="Preview/6.png" width="250" height="520"> <br>
<img src="Preview/7.png" width="250" height="520"> &nbsp;&nbsp;&nbsp;
<img src="Preview/8.png" width="250" height="520">&nbsp;&nbsp;&nbsp;
<img src="Preview/9.png" width="250" height="520"><br>
<img src="Preview/11.png" width="250" height="520"> &nbsp;&nbsp;&nbsp;

## Design Based on

<https://www.uistore.design/items/shopping-ui-kit-for-adobe-xd/>
