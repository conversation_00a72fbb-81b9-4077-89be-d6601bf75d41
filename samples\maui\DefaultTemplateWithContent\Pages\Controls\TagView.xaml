<?xml version="1.0" encoding="utf-8" ?>
<Border xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
        xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
        x:Class="DefaultTemplateWithContent.Pages.Controls.TagView"
        xmlns:models="clr-namespace:DefaultTemplateWithContent.Models"
        StrokeShape="RoundRectangle 16" 
        HeightRequest="32" 
        StrokeThickness="0" 
        Background="{Binding DisplayColor}"
        Padding="{OnPlatform '12,0,12,8',Android='12,0,12,0'}"
        x:DataType="models:Tag">
        
        <Label Text="{Binding Title}" x:Name="TitleLabel"
            TextColor="{AppThemeBinding Light={StaticResource LightBackground},Dark={StaticResource DarkBackground}}" 
            FontSize="14" 
            VerticalOptions="Center"
            VerticalTextAlignment="Center"/>
</Border>