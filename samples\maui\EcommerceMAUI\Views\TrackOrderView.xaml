<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="EcommerceMAUI.Views.TrackOrderView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:converters="clr-namespace:EcommerceMAUI.Converters"
    xmlns:vm="clr-namespace:EcommerceMAUI.ViewModel"
    Title="{Binding PageTitle}">
    <ContentPage.Resources>
        <ResourceDictionary>
            <converters:InverseBooleanConverter x:Key="InverseBoolConverter" />
        </ResourceDictionary>
    </ContentPage.Resources>
    <Grid>
        <StackLayout Margin="6,12" IsVisible="{Binding IsLoaded}">

            <CollectionView Margin="20" ItemsSource="{Binding TrackStatus}">
                <CollectionView.ItemTemplate>
                    <DataTemplate>
                        <Grid ColumnDefinitions="*,12,Auto,12,*">
                            <StackLayout Grid.Column="0">
                                <Label
                                    FontAttributes="Bold"
                                    FontSize="18"
                                    Text="{Binding DeliveryStatusDate, StringFormat='{0:dd MMMM yyyy}'}"
                                    TextColor="Gray" />
                                <Label
                                    FontAttributes="Bold"
                                    FontSize="14"
                                    Text="{Binding DeliveryStatusDate, StringFormat='{0:hh:mm:ss tt}'}"
                                    TextColor="Gray" />
                            </StackLayout>
                            <StackLayout Grid.Column="2" Spacing="0">
                                <Border
                                    Padding="4"
                                    Background="Transparent"
                                    Stroke="Black"
                                    StrokeShape="RoundRectangle 16"
                                    StrokeThickness="1">
                                    <Border
                                        Padding="4"
                                        Background="{Binding StatusColor}"
                                        HeightRequest="16"
                                        Stroke="{Binding StatusColor}"
                                        StrokeShape="RoundRectangle 16"
                                        StrokeThickness="1"
                                        WidthRequest="16" />
                                </Border>
                                <Border
                                    Background="{Binding StatusColor}"
                                    HeightRequest="80"
                                    HorizontalOptions="CenterAndExpand"
                                    IsVisible="{Binding IsLineVisible}"
                                    Stroke="{Binding StatusColor}"
                                    StrokeShape="RoundRectangle 16"
                                    StrokeThickness="1"
                                    VerticalOptions="FillAndExpand"
                                    WidthRequest="2" />
                            </StackLayout>
                            <StackLayout Grid.Column="4" Margin="20,0">
                                <Label
                                    FontAttributes="Bold"
                                    FontSize="16"
                                    Text="{Binding Name}"
                                    TextColor="Black" />
                                <Label
                                    FontAttributes="None"
                                    FontSize="12"
                                    Text="{Binding Location}"
                                    TextColor="Black" />
                            </StackLayout>
                        </Grid>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
        </StackLayout>
        <ActivityIndicator
            HeightRequest="45"
            HorizontalOptions="Center"
            IsRunning="{Binding IsLoaded, Converter={StaticResource InverseBoolConverter}}"
            VerticalOptions="Center"
            WidthRequest="45"
            Color="{StaticResource Primary}" />
    </Grid>

</ContentPage>