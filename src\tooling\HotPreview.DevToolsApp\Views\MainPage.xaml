﻿<Page x:Class="HotPreview.DevToolsApp.Views.MainPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:viewModels="using:HotPreview.DevToolsApp.ViewModels"
      xmlns:converters="using:HotPreview.DevToolsApp.Converters"
      xmlns:selectors="using:HotPreview.DevToolsApp.Selectors"
      xmlns:utu="using:Uno.Toolkit.UI"
      NavigationCacheMode="Required">

  <Page.Resources>
    <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>

    <!-- Section Header Template -->
    <DataTemplate x:Key="SectionItemTemplate" x:DataType="viewModels:NavTreeItemViewModel">
      <TreeViewItem ItemsSource="{x:Bind Children, Mode=OneTime}"
                    RightTapped="OnTreeViewItemRightTapped">
        <TreeViewItem.ContextFlyout>
          <MenuFlyout>
            <MenuFlyoutItem Text="Update snapshots" Click="OnUpdateSnapshotsClicked"/>
          </MenuFlyout>
        </TreeViewItem.ContextFlyout>
        <Border x:Name="SectionHeaderBorder"
                Background="Transparent"
                CornerRadius="0"
                Margin="-8,0,8,0">
          <TextBlock Text="{x:Bind DisplayName, Mode=OneTime}"
                     FontSize="12"
                     FontWeight="SemiBold"
                     Foreground="#9CA3AF"
                     VerticalAlignment="Center"/>
        </Border>
      </TreeViewItem>
    </DataTemplate>

    <!-- Default Item Template -->
    <DataTemplate x:Key="DefaultItemTemplate" x:DataType="viewModels:NavTreeItemViewModel">
      <TreeViewItem ItemsSource="{x:Bind Children, Mode=OneTime}"
                    RightTapped="OnTreeViewItemRightTapped">
        <TreeViewItem.ContextFlyout>
          <MenuFlyout>
            <MenuFlyoutItem Text="Update snapshots" Click="OnUpdateSnapshotsClicked"/>
          </MenuFlyout>
        </TreeViewItem.ContextFlyout>
        <StackPanel Orientation="Horizontal"
                    Margin="0,0,0,0">
          <!-- Icon -->
          <Viewbox Width="18"
              Height="18"
              Margin="-8,0,8,0">
            <Canvas Width="24"
                Height="24"
                VerticalAlignment="Center"
                HorizontalAlignment="Center">
              <Path Data="{x:Bind PathIcon, Mode=OneTime}"
                    Fill="{ThemeResource DefaultTextForegroundThemeBrush}"/>
            </Canvas>
          </Viewbox>

          <!-- Display Name -->
          <TextBlock Text="{x:Bind DisplayName, Mode=OneTime}"
                      FontSize="14"
                      VerticalAlignment="Center"/>
        </StackPanel>
      </TreeViewItem>
    </DataTemplate>

    <!-- Command Item Template (no context menu) -->
    <DataTemplate x:Key="CommandItemTemplate" x:DataType="viewModels:NavTreeItemViewModel">
      <TreeViewItem ItemsSource="{x:Bind Children, Mode=OneTime}">
        <StackPanel Orientation="Horizontal"
                    Margin="0,0,0,0">
          <!-- Icon -->
          <Viewbox Width="18"
              Height="18"
              Margin="-8,0,8,0">
            <Canvas Width="24"
                Height="24"
                VerticalAlignment="Center"
                HorizontalAlignment="Center">
              <Path Data="{x:Bind PathIcon, Mode=OneTime}"
                    Fill="{ThemeResource DefaultTextForegroundThemeBrush}"/>
            </Canvas>
          </Viewbox>

          <!-- Display Name -->
          <TextBlock Text="{x:Bind DisplayName, Mode=OneTime}"
                      FontSize="14"
                      VerticalAlignment="Center"/>
        </StackPanel>
      </TreeViewItem>
    </DataTemplate>

    <!-- Template Selector -->
    <selectors:NavTreeItemTemplateSelector x:Key="NavTreeItemTemplateSelector"
                                           SectionItemTemplate="{StaticResource SectionItemTemplate}"
                                           DefaultItemTemplate="{StaticResource DefaultItemTemplate}"
                                           CommandItemTemplate="{StaticResource CommandItemTemplate}"/>
  </Page.Resources>

  <Grid x:Name="PageRoot"
        utu:SafeArea.Insets="VisibleBounds">
    <Grid.RowDefinitions>
      <RowDefinition Height="Auto"/>
      <RowDefinition Height="Auto"/>
      <RowDefinition Height="*"/>
      <RowDefinition Height="Auto"/>
    </Grid.RowDefinitions>

    <!-- Header -->
    <Border Grid.Row="0"
            Background="#FF6B46C1"
            Padding="16,12">
      <Grid>
        <Grid.ColumnDefinitions>
          <ColumnDefinition Width="*"/>
          <ColumnDefinition Width="Auto"/>
          <ColumnDefinition Width="Auto"/>
        </Grid.ColumnDefinitions>

        <!-- Title with Logo -->
        <StackPanel Grid.Column="0"
                    Orientation="Horizontal"
                    VerticalAlignment="Center">
          <Border Background="#FF10B981"
                  CornerRadius="4"
                  Width="24"
                  Height="24"
                  Margin="0,0,8,0">
            <TextBlock Text="S"
                       FontWeight="Bold"
                       FontSize="14"
                       Foreground="White"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Center"/>
          </Border>
          <TextBlock Text="{x:Bind ViewModel.PageTitle, Mode=OneWay}"
                     FontSize="14"
                     FontWeight="SemiBold"
                     Foreground="White"
                     VerticalAlignment="Center"/>
        </StackPanel>

        <!-- Play Button -->
        <Button Grid.Column="1"
                Background="Transparent"
                BorderThickness="0"
                Margin="0,0,8,0"
                Command="{Binding PlayCommand}">
          <TextBlock Text="▶"
                     FontSize="16"
                     Foreground="White"/>
        </Button>

        <!-- Settings Button -->
        <Button Grid.Column="2"
                Background="Transparent"
                BorderThickness="0"
                Command="{Binding SettingsCommand}">
          <TextBlock Text="⚙"
                     FontSize="16"
                     Foreground="White"/>
        </Button>
      </Grid>
    </Border>

    <!-- Search Bar -->
    <Border Grid.Row="1"
            BorderBrush="#E5E7EB"
            BorderThickness="0,0,0,1"
            Padding="16,12">
      <AutoSuggestBox x:Name="SearchBox"
                      Text="{x:Bind ViewModel.SearchText, Mode=TwoWay}"
                      PlaceholderText="Find components"
                      QueryIcon="Find"
                      Background="Transparent"
                      BorderThickness="0"
                      FontSize="14"
                      IsEnabled="{x:Bind ViewModel.HaveApp, Mode=OneWay}"/>
    </Border>

    <!-- Navigation Tree Area -->
    <Grid Grid.Row="2">
      <!-- Navigation Tree -->
      <TreeView x:Name="NavTree"
                CanDragItems="False"
                CanReorderItems="False"
                ItemsSource="{x:Bind ViewModel.NavTreeItems}"
                ItemInvoked="OnNavTreeItemInvoked"
                Visibility="{x:Bind converters:Converters.ToVisibility(ViewModel.HaveApp), Mode=OneWay}">

        <TreeView.ItemContainerStyle>
          <!-- Tighten up the vertical spacing, reducing the default padding -->
          <Style TargetType="TreeViewItem">
            <Setter Property="Padding" Value="0,0,0,4"/>
          </Style>
        </TreeView.ItemContainerStyle>

        <TreeView.ItemTemplateSelector>
          <StaticResource ResourceKey="NavTreeItemTemplateSelector"/>
        </TreeView.ItemTemplateSelector>
      </TreeView>

      <!-- No App Connected Message -->
      <StackPanel HorizontalAlignment="Center"
                  VerticalAlignment="Center"
                  Visibility="{x:Bind converters:Converters.ToVisibilityInverted(ViewModel.HaveApp), Mode=OneWay}">
        <TextBlock Text="No app connected"
                   FontSize="16"
                   Foreground="#6B7280"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,8"/>
        <TextBlock Text="Start an app to see its components"
                   FontSize="14"
                   Foreground="#9CA3AF"
                   HorizontalAlignment="Center"/>
      </StackPanel>
    </Grid>

    <!-- Status Bar -->
    <Border Grid.Row="3"
            Background="{ThemeResource SystemControlBackgroundChromeMediumBrush}"
            BorderBrush="{ThemeResource SystemControlForegroundBaseMediumLowBrush}"
            BorderThickness="0,1,0,0"
            Padding="16,8">
      <Grid>
        <Grid.ColumnDefinitions>
          <ColumnDefinition Width="*"/>
          <ColumnDefinition Width="Auto"/>
        </Grid.ColumnDefinitions>

        <!-- Status Message -->
        <TextBlock Grid.Column="0"
                   Text="{x:Bind ViewModel.StatusMessage, Mode=OneWay}"
                   FontSize="12"
                   Foreground="{ThemeResource SystemControlForegroundBaseMediumBrush}"
                   VerticalAlignment="Center"/>

        <!-- Future: Additional status bar buttons will go in Grid.Column="1" -->
      </Grid>
    </Border>

  </Grid>
</Page>
