<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:pageModels="clr-namespace:DefaultTemplateWithContent.PageModels"
             xmlns:models="clr-namespace:DefaultTemplateWithContent.Models"
             xmlns:controls="clr-namespace:DefaultTemplateWithContent.Pages.Controls"
             xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
             x:Class="DefaultTemplateWithContent.Pages.ProjectListPage"
             x:DataType="pageModels:ProjectListPageModel"
             Title="Projects">


    <ContentPage.Behaviors>
        <toolkit:EventToCommandBehavior
                EventName="Appearing"                
                Command="{Binding AppearingCommand}" />
    </ContentPage.Behaviors>
    <Grid>
        <VerticalStackLayout 
            BindableLayout.ItemsSource="{Binding Projects}" 
            Margin="{StaticResource LayoutPadding}" 
            Spacing="{StaticResource LayoutSpacing}">
            <BindableLayout.ItemTemplate>
                <DataTemplate x:DataType="models:Project">
                    <Border>
                        <VerticalStackLayout Padding="10">
                            <Label Text="{Binding Name}" FontSize="24" />
                            <Label Text="{Binding Description}" />
                        </VerticalStackLayout>
                        <Border.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding NavigateToProjectCommand, Source={RelativeSource AncestorType={x:Type pageModels:ProjectListPageModel}}, x:DataType=pageModels:ProjectListPageModel}" CommandParameter="{Binding .}"/>
                        </Border.GestureRecognizers>
                    </Border>
                </DataTemplate>
            </BindableLayout.ItemTemplate>
        </VerticalStackLayout>

        <controls:AddButton 
            Command="{Binding AddProjectCommand}" />
    </Grid>
</ContentPage>