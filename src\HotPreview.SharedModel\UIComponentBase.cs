using System;
using System.Collections.Generic;
using System.Linq;

namespace HotPreview.SharedModel;

public abstract class UIComponentBase<TPreview>(UIComponentKind kind, string? displayNameOverride, IReadOnlyList<TPreview> previews) where TPreview : PreviewBase
{
    private readonly IReadOnlyList<TPreview> _previews = previews;

    /// <summary>
    /// Name is intended to be what's used by the code to identify the component. It's the component's
    /// full qualified type name and is unique.
    /// </summary>
    public abstract string Name { get; }

    public UIComponentCategory? Category { get; private set; }

    public UIComponentKind Kind { get; } = kind;

    /// <summary>
    /// DisplayName is intended to be what's shown in the UI to identify the component. It can contain spaces and
    /// isn't necessarily unique. It defaults to the class name (with no namespace qualifier) but can be
    /// overridden by the developer.
    /// </summary>
    public string DisplayName => DisplayNameOverride ?? NameUtilities.GetUnqualifiedName(Name);

    public string? DisplayNameOverride { get; } = displayNameOverride;

    /// <summary>
    /// Returns a path that can be used as a monochrome icon (e.g. via WinUI's PathIcon). The path here is assumed t
    /// be in a 24x24 viewport (the convention used by Material icons and most Fluent icons). If you have a path with
    /// a different viewport, you can scale it to 24x24 by applying a scale operation with
    /// https://yqnn.github.io/svg-path-editor/ or similar tools.
    ///
    /// TODO: Make this customizable with UIComponent attribute.
    /// </summary>
    public string PathIcon =>
        Kind switch
        {
            // From https://github.com/microsoft/fluentui-system-icons/blob/main/assets/Button/SVG/ic_fluent_button_20_regular.svg, scaled from 20x20 to 24x24
            UIComponentKind.Control => "M 2.4 9.6 C 2.4 7.6118 4.0118 6 6 6 H 18 C 19.9883 6 21.6 7.6118 21.6 9.6 V 13.2 C 21.6 15.1883 19.9883 16.8 18 16.8 H 6 C 4.0118 16.8 2.4 15.1883 2.4 13.2 V 9.6 Z M 6 7.2 C 4.6745 7.2 3.6 8.2745 3.6 9.6 V 13.2 C 3.6 14.5255 4.6745 15.6 6 15.6 H 18 C 19.3255 15.6 20.4 14.5255 20.4 13.2 V 9.6 C 20.4 8.2745 19.3255 7.2 18 7.2 H 6 Z M 10.8 11.4 C 10.8 11.0686 11.0686 10.8 11.4 10.8 H 16.8 C 17.1313 10.8 17.4 11.0686 17.4 11.4 C 17.4 11.7314 17.1313 12 16.8 12 H 11.4 C 11.0686 12 10.8 11.7314 10.8 11.4 Z M 9.6 11.4 C 9.6 12.3941 8.7941 13.2 7.8 13.2 C 6.8059 13.2 6 12.3941 6 11.4 C 6 10.4059 6.8059 9.6 7.8 9.6 C 8.7941 9.6 9.6 10.4059 9.6 11.4 Z",

            // From https://github.com/microsoft/fluentui-system-icons/blob/main/assets/Document%20One%20Page/SVG/ic_fluent_document_one_page_24_regular.svg
            _ => "M6.25 2C5.00736 2 4 3.00736 4 4.25V19.75C4 20.9926 5.00736 22 6.25 22H17.75C18.9926 22 20 20.9926 20 19.75V4.25C20 3.00736 18.9926 2 17.75 2H6.25ZM5.5 4.25C5.5 3.83579 5.83579 3.5 6.25 3.5H17.75C18.1642 3.5 18.5 3.83579 18.5 4.25V19.75C18.5 20.1642 18.1642 20.5 17.75 20.5H6.25C5.83579 20.5 5.5 20.1642 5.5 19.75V4.25ZM7.75 6.5C7.33579 6.5 7 6.83579 7 7.25C7 7.66421 7.33579 8 7.75 8H16.25C16.6642 8 17 7.66421 17 7.25C17 6.83579 16.6642 6.5 16.25 6.5H7.75ZM7 16.25C7 15.8358 7.33579 15.5 7.75 15.5H16.25C16.6642 15.5 17 15.8358 17 16.25C17 16.6642 16.6642 17 16.25 17H7.75C7.33579 17 7 16.6642 7 16.25ZM7.75 11C7.33579 11 7 11.3358 7 11.75C7 12.1642 7.33579 12.5 7.75 12.5H16.25C16.6642 12.5 17 12.1642 17 11.75C17 11.3358 16.6642 11 16.25 11H7.75Z"
        };

    public bool HasPreview => _previews.Count >= 0;

    public bool HasNoPreviews => _previews.Count == 0;

    public bool HasSinglePreview => _previews.Count == 1;

    public bool HasMultiplePreviews => _previews.Count > 1;

    public IReadOnlyList<TPreview> Previews => _previews;

    public TPreview? GetPreview(string name)
    {
        foreach (TPreview preview in _previews)
        {
            if (preview.Name.Equals(name, StringComparison.Ordinal))
            {
                return preview;
            }
        }

        return null;
    }

    public TPreview DefaultPreview
    {
        get
        {
            if (_previews.Count == 0)
            {
                throw new InvalidOperationException($"Component '{Name}' has no previews");
            }

            // Currently, the default preview is always the first one, though we may allow
            // it to be set explicitly in the future
            return _previews[0];
        }
    }

    /// <summary>
    /// Creates a copy of this UI component with an additional preview.
    /// If the new preview is not auto-generated, removes any auto-generated previews from the result.
    /// </summary>
    /// <param name="preview">The preview to add</param>
    /// <returns>A new UI component instance with the added preview</returns>
    public abstract UIComponentBase<TPreview> WithAddedPreview(TPreview preview);

    public bool IsAutoGenerated => _previews.All(e => e.IsAutoGenerated);

    /// <summary>
    /// Helper method for subclasses to apply the auto-generated preview removal logic for WithAddedPreview.
    /// </summary>
    protected IReadOnlyList<TPreview> GetUpdatedPreviews(TPreview newPreview)
    {
        List<TPreview> updatedPreviews = _previews.ToList();
        updatedPreviews.Add(newPreview);

        // If there's a user defined preview, remove any auto-generated previews
        if (!newPreview.IsAutoGenerated)
        {
            updatedPreviews.RemoveAll(e => e.IsAutoGenerated);
        }

        return updatedPreviews;
    }
}
