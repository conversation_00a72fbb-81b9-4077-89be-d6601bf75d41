using DefaultTemplateWithContent.Models;
using HotPreview.App.Maui;

namespace DefaultTemplateWithContent.Pages;
public partial class ProjectDetailPage : ContentPage
{
    public ProjectDetailPage(ProjectDetailPageModel model)
    {
        InitializeComponent();

        BindingContext = model;
    }
}

public class ChipDataTemplateSelector : DataTemplateSelector
{
    public required DataTemplate SelectedTagTemplate { get; set; }
    public required DataTemplate NormalTagTemplate { get; set; }

    protected override DataTemplate OnSelectTemplate(object item, BindableObject container)
    {
        return (item as Tag)?.IsSelected ?? false ? SelectedTagTemplate : NormalTagTemplate;
    }
}
